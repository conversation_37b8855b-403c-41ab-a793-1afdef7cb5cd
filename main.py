from PIL import Image, ImageDraw, ImageFont, ImageEnhance
import os
import sys
import locale

# Universal UTF-8 setup for any locale
try:
    # Try to set UTF-8 encoding for stdin/stdout
    if hasattr(sys.stdin, 'reconfigure'):
        sys.stdin.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    if hasattr(sys.stderr, 'reconfigure'):
        sys.stderr.reconfigure(encoding='utf-8', errors='replace')
except:
    pass

# Set environment variables for UTF-8
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Try to set locale to UTF-8, fallback to C.UTF-8 if current locale fails
try:
    locale.setlocale(locale.LC_ALL, '')
    if not locale.getlocale()[1] or 'utf' not in locale.getlocale()[1].lower():
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_ALL, 'C.UTF-8')
    except:
        pass

from font_detector import find_font

if len(sys.argv) < 2:
    print('Usage: python main.py <background_image>')
    sys.exit(1)
background_image = sys.argv[1]
with Image.open(os.path.join('backgrounds', background_image)).convert('RGBA') as f:
    print(f'Background image: {background_image}')
    print('='*60)
    text_to_write = input('Text to write: ')
    text_color = input('Text color (R,G,B): ')
    stroke_color = input('Stroke color (R,G,B): ')
    stroke_width = int(input('Stroke width: '))
    stroke_color = tuple(map(int, stroke_color.split(',')))
    text_color = tuple(map(int, text_color.split(',')))
    text = Image.new('RGBA', f.size, (0, 0, 0, 0))  # Przezroczyste tło
    selected_font = input('Font: ')
    font_result = find_font(selected_font)

    if not font_result['found']:
        print(f"Warning: Font '{selected_font}' not found!")
        sys.exit(1)

    if font_result['is_local_font']:
        font_path = font_result['local_path']
    else:
        font_path = font_result['system_path']

    selected_font_size = int(input('Font size: '))
    font = ImageFont.truetype(font_path, selected_font_size)
    draw = ImageDraw.Draw(text)
    bbox = font.getbbox(text_to_write)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_cords = ((f.width - text_width) // 2, (f.height - text_height) // 2)
    draw.text(text_cords, text_to_write, fill=text_color, stroke_width=stroke_width, stroke_fill=stroke_color, font=font)
    output = Image.alpha_composite(f, text)
    output.save(os.path.join('output', f'{text_to_write}.png'), quality=100)
    print(f'The image has been saved to "{os.path.join("output", f"{text_to_write}.png")}"')
    output.show()
