import os
import platform
import sys
from pathlib import Path

def get_system_font_paths():
    """Get system font directories based on the operating system."""
    system = platform.system().lower()
    
    if system == 'windows':
        return [
            os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts'),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Microsoft', 'Windows', 'Fonts')
        ]
    elif system == 'darwin':  # macOS
        return [
            '/System/Library/Fonts',
            '/Library/Fonts',
            os.path.expanduser('~/Library/Fonts')
        ]
    else:  # Linux and other Unix-like systems
        return [
            '/usr/share/fonts',
            '/usr/local/share/fonts',
            os.path.expanduser('~/.fonts'),
            os.path.expanduser('~/.local/share/fonts')
        ]

def find_font(font_name):
    """
    Find font in system and local directories.
    Returns dict with font location info.
    """
    font_extensions = ['.ttf', '.otf', '.woff', '.woff2', '.eot']
    
    # Remove extension if provided
    font_base = os.path.splitext(font_name)[0]
    
    result = {
        'system_path': None,
        'local_path': None,
        'is_system_font': False,
        'is_local_font': False,
        'found': False
    }
    
    # Check local fonts directory
    local_fonts_dir = 'fonts'
    if os.path.exists(local_fonts_dir):
        for ext in font_extensions:
            local_font_path = os.path.join(local_fonts_dir, font_base + ext)
            if os.path.exists(local_font_path):
                result['local_path'] = local_font_path
                result['is_local_font'] = True
                result['found'] = True
                break
    
    # Check system font directories
    system_paths = get_system_font_paths()
    for font_dir in system_paths:
        if not os.path.exists(font_dir):
            continue
            
        for ext in font_extensions:
            system_font_path = os.path.join(font_dir, font_base + ext)
            if os.path.exists(system_font_path):
                result['system_path'] = system_font_path
                result['is_system_font'] = True
                result['found'] = True
                break
        
        if result['is_system_font']:
            break
    
    return result

def main():
    if len(sys.argv) > 1:
        font_name = sys.argv[1]
    else:
        font_name = input("Enter font name: ")
    
    result = find_font(font_name)
    
    print(f"Font search results for '{font_name}':")
    print("=" * 50)
    
    if result['found']:
        if result['is_local_font']:
            print(f"✓ Custom/Local font found: {result['local_path']}")
        if result['is_system_font']:
            print(f"✓ System font found: {result['system_path']}")
    else:
        print("✗ Font not found in system or local directories")
    
    return result

if __name__ == "__main__":
    main()